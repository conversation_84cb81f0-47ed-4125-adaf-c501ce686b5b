// 动态发布助手 - 后台脚本 (Service Worker)

// 支持的平台列表
const SUPPORTED_PLATFORMS = [
  { 
    id: 'weibo', 
    name: '微博', 
    publishUrl: 'https://weibo.com/compose', 
    color: 'bg-red-500'
  },
  { 
    id: 'xiaoh<PERSON><PERSON>', 
    name: '小红书', 
    publishUrl: 'https://creator.xiaohongshu.com/publish/publish', 
    color: 'bg-red-500'
  },
  { 
    id: 'jike', 
    name: '即刻', 
    publishUrl: 'https://web.okjike.com', 
    color: 'bg-yellow-500'
  },
  { 
    id: 'douyin', 
    name: '抖音', 
    publishUrl: 'https://creator.douyin.com/creator-micro/content/upload', 
    color: 'bg-black'
  }
];

// 全局状态
let publishState = {
  isPublishing: false,
  currentTasks: [],
  publishResults: []
};

// 任务调度器
class TaskScheduler {
  constructor() {
    this.maxConcurrency = 2; // 最大并发数
    this.activeJobs = new Map();
    this.taskQueue = [];
  }

  async executeTasks(platforms, content) {
    console.log('Starting publish tasks for platforms:', platforms);
    
    publishState.isPublishing = true;
    publishState.currentTasks = platforms;
    publishState.publishResults = [];

    // 保存状态到存储
    await this.saveState();

    // 通知侧边栏发布开始
    this.broadcastMessage({
      action: 'publishStarted',
      data: { platforms, content }
    });

    // 创建任务
    const tasks = platforms.map(platform => ({
      platform,
      content,
      execute: () => this.executeTask(platform, content)
    }));

    // 分批执行，控制并发
    const chunks = this.chunkArray(tasks, this.maxConcurrency);
    
    for (const chunk of chunks) {
      await Promise.allSettled(
        chunk.map(task => task.execute())
      );
    }

    publishState.isPublishing = false;
    await this.saveState();

    // 通知发布完成
    this.broadcastMessage({
      action: 'publishCompleted',
      data: { results: publishState.publishResults }
    });
  }

  async executeTask(platform, content) {
    console.log(`Executing task for platform: ${platform.name}`);
    
    try {
      // 更新状态为发布中
      this.updatePublishResult({
        platform,
        status: 'publishing',
        message: '正在打开发布页面...',
        timestamp: Date.now()
      });

      // 1. 打开平台发布页面
      const tab = await chrome.tabs.create({ 
        url: platform.publishUrl, 
        active: false 
      });

      console.log(`Created tab ${tab.id} for ${platform.name}`);

      // 等待页面加载
      await this.waitForTabLoad(tab.id);

      // 更新状态
      this.updatePublishResult({
        platform,
        status: 'publishing',
        message: '页面加载完成，准备注入脚本...',
        timestamp: Date.now()
      });

      // 2. 注入内容脚本
      await this.injectContentScript(tab.id, platform);

      // 3. 发送发布指令
      await chrome.tabs.sendMessage(tab.id, {
        action: 'publish',
        data: content
      });

      // 更新状态
      this.updatePublishResult({
        platform,
        status: 'publishing',
        message: '正在执行发布操作...',
        timestamp: Date.now()
      });

      // 4. 等待发布完成（这里简化处理，实际应该监听内容脚本的反馈）
      await this.delay(5000); // 等待5秒模拟发布过程

      // 5. 发布成功
      this.updatePublishResult({
        platform,
        status: 'success',
        message: '发布成功',
        publishUrl: platform.publishUrl,
        timestamp: Date.now()
      });

      // 6. 延迟关闭标签页
      setTimeout(() => {
        chrome.tabs.remove(tab.id).catch(console.error);
      }, 2000);

    } catch (error) {
      console.error(`Failed to publish to ${platform.name}:`, error);
      
      this.updatePublishResult({
        platform,
        status: 'failed',
        message: error.message || '发布失败',
        timestamp: Date.now()
      });
    }
  }

  async waitForTabLoad(tabId) {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('Tab load timeout'));
      }, 30000); // 30秒超时

      const checkTab = () => {
        chrome.tabs.get(tabId, (tab) => {
          if (chrome.runtime.lastError) {
            clearTimeout(timeout);
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }

          if (tab.status === 'complete') {
            clearTimeout(timeout);
            resolve();
          } else {
            setTimeout(checkTab, 1000);
          }
        });
      };

      checkTab();
    });
  }

  async injectContentScript(tabId, platform) {
    try {
      await chrome.scripting.executeScript({
        target: { tabId },
        files: [`content-scripts/adapters/${platform.id}.js`]
      });
      console.log(`Injected content script for ${platform.name}`);
    } catch (error) {
      console.error(`Failed to inject content script for ${platform.name}:`, error);
      throw error;
    }
  }

  updatePublishResult(result) {
    // 更新结果数组
    const existingIndex = publishState.publishResults.findIndex(
      r => r.platform.id === result.platform.id
    );

    if (existingIndex >= 0) {
      publishState.publishResults[existingIndex] = result;
    } else {
      publishState.publishResults.push(result);
    }

    // 保存状态
    this.saveState();

    // 广播更新
    this.broadcastMessage({
      action: 'publishResult',
      data: result
    });
  }

  broadcastMessage(message) {
    // 发送消息到所有扩展页面
    chrome.runtime.sendMessage(message).catch(() => {
      // 忽略没有接收者的错误
    });
  }

  async saveState() {
    try {
      await chrome.storage.local.set({
        publishStatus: {
          isPublishing: publishState.isPublishing,
          timestamp: Date.now()
        },
        publishResults: publishState.publishResults
      });
    } catch (error) {
      console.error('Failed to save state:', error);
    }
  }

  chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 创建任务调度器实例
const taskScheduler = new TaskScheduler();

// 监听扩展图标点击事件
chrome.action.onClicked.addListener(async (tab) => {
  console.log('Extension icon clicked');

  try {
    // 创建新标签页并打开主页面
    const newTab = await chrome.tabs.create({
      url: chrome.runtime.getURL('main/main.html'),
      active: true
    });

    console.log('Created new tab:', newTab.id);
  } catch (error) {
    console.error('Failed to create new tab:', error);
  }
});

// 监听消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background received message:', message);

  if (message.action === 'startPublish') {
    handlePublishRequest(message.data);
    sendResponse({ success: true });
  } else if (message.action === 'retryPublish') {
    handleRetryRequest(message.data);
    sendResponse({ success: true });
  } else if (message.action === 'getPublishStatus') {
    sendResponse({
      isPublishing: publishState.isPublishing,
      results: publishState.publishResults
    });
  }

  return true; // 保持消息通道开放
});

async function handlePublishRequest(data) {
  const { title, content, platforms } = data;
  
  console.log('Handling publish request:', { title, content, platforms });

  if (!content || !platforms || platforms.length === 0) {
    console.error('Invalid publish data');
    return;
  }

  try {
    await taskScheduler.executeTasks(platforms, { title, content });
  } catch (error) {
    console.error('Failed to execute publish tasks:', error);
  }
}

async function handleRetryRequest(data) {
  const { platform } = data;
  
  console.log('Handling retry request for platform:', platform);

  try {
    // 获取当前内容
    const result = await chrome.storage.local.get(['publishData']);
    if (result.publishData) {
      const { title, content } = result.publishData;
      await taskScheduler.executeTask(platform, { title, content });
    }
  } catch (error) {
    console.error('Failed to retry publish:', error);
  }
}

// 监听标签页更新，确保内容脚本正确注入
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // 检查是否是支持的平台
    const supportedDomains = [
      'weibo.com',
      'xiaohongshu.com', 
      'okjike.com',
      'douyin.com'
    ];
    
    const isSupported = supportedDomains.some(domain => tab.url.includes(domain));
    
    if (isSupported) {
      const platform = getPlatformFromUrl(tab.url);
      if (platform) {
        console.log(`Tab updated for platform: ${platform}`);
        // 内容脚本会通过 manifest.json 自动注入
      }
    }
  }
});

function getPlatformFromUrl(url) {
  if (url.includes('weibo.com')) return 'weibo';
  if (url.includes('xiaohongshu.com')) return 'xiaohongshu';
  if (url.includes('okjike.com')) return 'jike';
  if (url.includes('douyin.com')) return 'douyin';
  return null;
}

// 扩展安装时的初始化
chrome.runtime.onInstalled.addListener(() => {
  console.log('动态发布助手已安装');
  
  // 清理旧数据
  chrome.storage.local.clear().catch(console.error);
});

// Service Worker 启动时恢复状态
chrome.runtime.onStartup.addListener(async () => {
  try {
    const result = await chrome.storage.local.get(['publishStatus', 'publishResults']);
    if (result.publishStatus) {
      publishState.isPublishing = result.publishStatus.isPublishing || false;
    }
    if (result.publishResults) {
      publishState.publishResults = result.publishResults;
    }
    console.log('Background state restored:', publishState);
  } catch (error) {
    console.error('Failed to restore state:', error);
  }
});

console.log('动态发布助手后台脚本已加载');
