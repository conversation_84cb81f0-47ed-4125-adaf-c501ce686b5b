// 抖音平台适配器
console.log('抖音内容脚本已加载');

class DouyinAdapter {
  constructor() {
    this.platform = 'douyin';
    this.selectors = {
      contentTextarea: 'textarea[placeholder*="添加作品描述"]',
      publishButton: 'button:contains("发布")',
      imageUpload: 'input[type="file"][accept*="image"]'
    };
  }

  async waitForElement(selector, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const check = () => {
        let element;
        
        // 处理包含文本的选择器
        if (selector.includes(':contains(')) {
          const text = selector.match(/:contains\("([^"]+)"\)/)[1];
          const tagName = selector.split(':')[0];
          const elements = document.querySelectorAll(tagName);
          element = Array.from(elements).find(el => el.textContent.includes(text));
        } else {
          element = document.querySelector(selector);
        }
        
        if (element) {
          resolve(element);
          return;
        }
        
        if (Date.now() - startTime > timeout) {
          reject(new Error(`Element ${selector} not found within ${timeout}ms`));
          return;
        }
        
        setTimeout(check, 100);
      };
      
      check();
    });
  }

  async fillContent(selector, content) {
    try {
      const element = await this.waitForElement(selector);
      
      // 清空现有内容
      element.value = '';
      element.focus();
      
      // 模拟用户输入
      element.value = content;
      
      // 触发输入事件
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      
      console.log('Content filled successfully');
      return true;
    } catch (error) {
      console.error('Failed to fill content:', error);
      throw error;
    }
  }

  async clickElement(selector) {
    try {
      const element = await this.waitForElement(selector);
      element.click();
      console.log('Element clicked successfully');
      return true;
    } catch (error) {
      console.error('Failed to click element:', error);
      throw error;
    }
  }

  async publish(data) {
    console.log('开始抖音发布流程:', data);
    
    try {
      const { title, content } = data;
      const fullContent = title ? `${title}\n${content}` : content;
      
      // 1. 等待页面加载完成
      await new Promise(resolve => setTimeout(resolve, 5000));
      console.log('抖音发布页面加载完成');
      
      // 2. 填充内容
      await this.fillContent(this.selectors.contentTextarea, fullContent);
      console.log('内容填充完成');
      
      // 3. 等待一下让页面处理
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 4. 点击发布按钮
      await this.clickElement(this.selectors.publishButton);
      console.log('发布按钮已点击');
      
      // 5. 等待发布完成
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      console.log('抖音发布完成');
      
      return {
        success: true,
        message: '发布成功',
        url: window.location.href
      };
      
    } catch (error) {
      console.error('抖音发布失败:', error);
      return {
        success: false,
        message: error.message || '发布失败'
      };
    }
  }
}

// 创建适配器实例
const douyinAdapter = new DouyinAdapter();

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('抖音内容脚本收到消息:', message);
  
  if (message.action === 'publish') {
    douyinAdapter.publish(message.data)
      .then(result => {
        console.log('抖音发布结果:', result);
        sendResponse(result);
      })
      .catch(error => {
        console.error('抖音发布错误:', error);
        sendResponse({
          success: false,
          message: error.message || '发布失败'
        });
      });
    
    return true; // 保持消息通道开放
  }
});

console.log('抖音适配器初始化完成');
