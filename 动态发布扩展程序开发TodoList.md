# 动态发布扩展程序开发 TodoList

## 项目概览
- **项目名称**: 动态发布助手
- **目标平台**: 微博、小红书、即刻、抖音
- **开发周期**: 8周
- **技术栈**: 原生Chrome扩展 + 原生JavaScript/TypeScript + Tailwind CSS
- **构建工具**: TypeScript编译器 + Plasmo (仅用于最终打包发布)
- **重要说明**: 开发阶段使用原生DOM操作，避免CSP (Content Security Policy) 违规

---

## 第一阶段：基础框架搭建 (Week 1-2)

### Week 1: 项目初始化和环境配置

#### 1.1 开发环境准备
- [ ] 安装 Node.js (v18+) 和 npm
- [ ] 安装 TypeScript: `npm install -g typescript`
- [ ] 验证开发环境是否正常
- [ ] 确保Chrome浏览器已安装并启用开发者模式

#### 1.2 手动创建Chrome扩展项目结构
- [ ] 在 `MomentDots` 目录创建标准Chrome扩展目录结构
  ```
  MomentDots/
  ├── manifest.json
  ├── popup/
  │   ├── popup.html
  │   ├── popup.js
  │   └── popup.css
  ├── sidepanel/
  │   ├── sidepanel.html
  │   ├── sidepanel.js
  │   └── sidepanel.css
  ├── background/
  │   └── background.js
  ├── content-scripts/
  ├── configs/
  ├── shared/
  └── assets/
  ```
- [ ] 创建基础 `package.json` 文件
- [ ] 安装核心依赖包 (注意CSP兼容性)
  ```bash
  # 仅安装必要的非React依赖，避免CSP违规
  npm install zustand tailwindcss
  npm install @types/chrome typescript --save-dev
  # 注意: 不安装React相关依赖，使用原生DOM操作
  ```
- [ ] 配置 TypeScript 编译环境
- [ ] 配置 Tailwind CSS 构建流程

#### 1.3 UI组件迁移
- [ ] 创建 `src/popup` 目录
- [ ] 从 `content-sync-app` 复制组件到 `src/popup/components/`
- [ ] 从 `content-sync-app` 复制样式到 `src/popup/styles/`
- [ ] 从 `content-sync-app` 复制工具函数到 `src/popup/lib/`
- [ ] 创建 `src/sidepanel` 目录
- [ ] 从 `extension-sidebar` 复制组件到 `src/sidepanel/components/`
- [ ] 从 `extension-sidebar` 复制样式到 `src/sidepanel/styles/`

#### 1.4 基础配置文件
- [ ] 手动编写 `manifest.json` 文件
  - [ ] 配置 manifest_version: 3
  - [ ] 设置扩展基本信息 (name, version, description)
  - [ ] 配置 action (popup)
  - [ ] 配置 side_panel
  - [ ] 设置 background service worker
  - [ ] 配置 permissions 和 host_permissions
  - [ ] 设置扩展图标
- [ ] 创建 TypeScript 配置文件 `tsconfig.json`
- [ ] 创建 Tailwind CSS 配置文件
- [ ] 设置构建脚本 (TypeScript 编译)

### Week 2: 核心页面实现

#### 2.1 主页面 (Popup) 开发
- [ ] 创建 `popup/popup.html` 基础结构
- [ ] 创建 `popup/popup.ts` 主逻辑文件
- [ ] 从 `content-sync-app` 迁移并适配组件
  - [ ] 标题输入框
  - [ ] 内容文本域
  - [ ] 图片上传功能
- [ ] 实现平台选择组件
  - [ ] 微博选择器
  - [ ] 小红书选择器
  - [ ] 即刻选择器
  - [ ] 抖音选择器
- [ ] 实现"开始同步"按钮功能
- [ ] 添加表单验证逻辑
- [ ] 编译 TypeScript 到 JavaScript

#### 2.2 侧边栏 (SidePanel) 开发
- [ ] 创建 `sidepanel/sidepanel.html` 基础结构
- [ ] 创建 `sidepanel/sidepanel.ts` 主逻辑文件
- [ ] 从 `extension-sidebar` 迁移并适配组件
  - [ ] 平台状态列表
  - [ ] 发布进度指示器
  - [ ] 错误信息显示
- [ ] 实现重试按钮功能
- [ ] 添加实时状态更新机制
- [ ] 编译 TypeScript 到 JavaScript

#### 2.3 状态管理系统
- [ ] 创建 `shared/stores/publish-store.ts`
- [ ] 实现 Zustand store 配置
- [ ] 定义核心数据结构和类型
- [ ] 实现状态持久化 (Chrome Storage API)
- [ ] 编译状态管理模块

#### 2.4 开发者模式测试
- [ ] 在Chrome中加载扩展文件夹 (开发者模式)
- [ ] 测试扩展加载和显示
- [ ] 测试主页面表单功能
- [ ] 测试侧边栏显示
- [ ] 测试页面间基础通信
- [ ] 使用Chrome DevTools调试

---

## 第二阶段：核心平台适配 (Week 3-5)

### Week 3: 平台配置和基础适配器

#### 3.1 配置文件系统
- [ ] 创建 `src/configs/platforms/` 目录
- [ ] 创建微博配置文件 `weibo.json`
  - [ ] 定义页面URL和选择器
  - [ ] 配置操作步骤序列
  - [ ] 设置等待条件
- [ ] 创建小红书配置文件 `xiaohongshu.json`
- [ ] 创建即刻配置文件 `jike.json`
- [ ] 创建抖音配置文件 `douyin.json`

#### 3.2 基础适配器框架
- [ ] 创建 `content-scripts/shared/base-adapter.ts`
- [ ] 实现基础适配器类
  - [ ] 智能等待方法 `waitForElement`
  - [ ] 内容填充方法 `fillContent`
  - [ ] 图片上传方法 `uploadImages`
  - [ ] 元素点击方法 `clickElement`
- [ ] 创建工具函数 `content-scripts/shared/utils.ts`
- [ ] 实现错误处理和重试机制
- [ ] 编译 TypeScript 到 JavaScript

#### 3.3 平台适配器开发
- [ ] 创建微博适配器 `content-scripts/adapters/weibo.ts`
- [ ] 创建小红书适配器 `content-scripts/adapters/xiaohongshu.ts`
- [ ] 创建即刻适配器 `content-scripts/adapters/jike.ts`
- [ ] 创建抖音适配器 `content-scripts/adapters/douyin.ts`
- [ ] 编译所有适配器到 JavaScript

### Week 4: 微博和小红书适配器实现

#### 4.1 微博适配器详细实现
- [ ] 研究微博发布页面结构
- [ ] 实现文本内容填充
- [ ] 实现图片上传功能
- [ ] 实现发布按钮点击
- [ ] 添加发布成功检测
- [ ] 实现错误处理和状态反馈

#### 4.2 小红书适配器详细实现
- [ ] 研究小红书创作平台页面结构
- [ ] 实现笔记标题和内容填充
- [ ] 实现图片上传和排序
- [ ] 实现标签添加功能
- [ ] 实现发布按钮点击
- [ ] 添加发布成功检测

#### 4.3 适配器测试
- [ ] 使用Playwright MCP工具创建测试页面
- [ ] 单独测试微博适配器
  - [ ] Playwright自动化验证
  - [ ] Chrome开发者模式真实测试
- [ ] 单独测试小红书适配器
  - [ ] Playwright自动化验证
  - [ ] Chrome开发者模式真实测试
- [ ] 测试错误处理机制
- [ ] 优化等待时间和重试逻辑

### Week 5: 即刻和抖音适配器实现

#### 5.1 即刻适配器详细实现
- [ ] 研究即刻发布页面结构
- [ ] 实现动态内容填充
- [ ] 实现图片上传功能
- [ ] 实现发布按钮点击
- [ ] 添加发布成功检测

#### 5.2 抖音适配器详细实现
- [ ] 研究抖音创作平台页面结构
- [ ] 实现动态内容填充
- [ ] 实现图片上传功能
- [ ] 实现发布设置配置
- [ ] 实现发布按钮点击
- [ ] 添加发布成功检测

#### 5.3 全平台集成测试
- [ ] 使用Playwright MCP工具进行自动化测试
  - [ ] 创建模拟测试环境
  - [ ] 测试所有平台适配器
  - [ ] 验证配置文件正确性
- [ ] Chrome开发者模式真实环境测试
  - [ ] 加载扩展到Chrome浏览器
  - [ ] 测试完整发布流程
  - [ ] 验证各平台实际发布效果
- [ ] 测试异常情况处理
- [ ] 优化性能和稳定性

---

## 第三阶段：任务调度和状态管理 (Week 6-7)

### Week 6: 后台服务和任务调度

#### 6.1 后台脚本开发
- [ ] 创建 `background/background.ts`
- [ ] 实现 Service Worker 基础框架
- [ ] 添加消息监听和处理机制
- [ ] 实现扩展生命周期管理
- [ ] 编译 TypeScript 到 JavaScript

#### 6.2 任务调度器实现
- [ ] 创建 `background/task-scheduler.ts`
- [ ] 实现并发控制逻辑 (最大2个并发)
- [ ] 实现任务队列管理
- [ ] 添加任务状态跟踪
- [ ] 实现失败重试机制
- [ ] 编译调度器模块

#### 6.3 标签页管理器
- [ ] 创建 `background/tab-manager.ts`
- [ ] 实现标签页创建和管理
- [ ] 实现内容脚本注入
- [ ] 添加标签页状态监控
- [ ] 实现标签页清理机制
- [ ] 编译标签页管理模块

#### 6.4 消息通信系统
- [ ] 实现 popup 到 background 通信
- [ ] 实现 background 到 content script 通信
- [ ] 实现 content script 到 sidepanel 通信
- [ ] 添加消息类型定义和验证

### Week 7: 状态管理和用户反馈

#### 7.1 状态同步机制
- [ ] 实现实时状态更新
- [ ] 添加状态持久化
- [ ] 实现跨组件状态同步
- [ ] 添加状态恢复机制

#### 7.2 用户反馈系统
- [ ] 实现发布进度显示
- [ ] 添加成功/失败状态提示
- [ ] 实现错误信息展示
- [ ] 添加重试按钮功能

#### 7.3 侧边栏功能完善
- [ ] 实现平台状态实时更新
- [ ] 添加发布链接显示
- [ ] 实现单个平台重试
- [ ] 添加全部重试功能

#### 7.4 双重验证测试
- [ ] Playwright MCP自动化测试
  - [ ] 测试完整发布流程
  - [ ] 测试并发发布功能
  - [ ] 测试错误处理和重试
  - [ ] 测试状态同步机制
- [ ] Chrome开发者模式真实测试
  - [ ] 加载扩展进行实际测试
  - [ ] 验证各平台真实发布效果
  - [ ] 测试用户交互体验
  - [ ] 验证性能和稳定性

---

## 第四阶段：优化和测试 (Week 8)

### Week 8: 最终优化和构建发布

#### 8.1 性能优化
- [ ] 优化内存使用
- [ ] 减少不必要的网络请求
- [ ] 优化图片处理性能
- [ ] 改进页面加载速度

#### 8.2 用户体验优化
- [ ] 优化界面响应速度
- [ ] 改进错误提示信息
- [ ] 添加加载动画和进度指示
- [ ] 优化移动端适配

#### 8.3 最终验证测试
- [ ] Playwright MCP全面自动化测试
  - [ ] 压力测试 (大量并发发布)
  - [ ] 网络异常测试
  - [ ] 平台页面变化适应性测试
- [ ] Chrome开发者模式最终测试
  - [ ] 长时间运行稳定性测试
  - [ ] 不同Chrome版本兼容性测试
  - [ ] 不同操作系统测试
  - [ ] 不同屏幕分辨率测试

#### 8.4 Plasmo构建和打包
- [ ] 安装 Plasmo CLI: `npm install -g plasmo`
- [ ] 配置 Plasmo 构建环境
- [ ] 使用 Plasmo 构建生产版本
  ```bash
  plasmo build
  plasmo package
  ```
- [ ] 生成优化后的扩展包
- [ ] 验证打包后的扩展功能

#### 8.5 发布准备
- [ ] 完善用户使用文档
- [ ] 创建安装和配置指南
- [ ] 准备应用商店发布材料
- [ ] 创建版本发布说明
- [ ] 准备扩展商店截图和描述

---

## 持续任务 (贯穿整个开发过程)

### 代码质量
- [ ] 保持代码规范和注释
- [ ] 定期进行代码审查
- [ ] 维护类型定义的准确性
- [ ] 保持组件的可复用性

### 测试覆盖
- [ ] 为核心功能编写单元测试
- [ ] 定期进行集成测试
- [ ] 记录和修复发现的bug
- [ ] 维护测试用例文档

### 安全和合规
- [ ] 确保用户数据安全
- [ ] 遵守各平台使用条款
- [ ] 实现最小权限原则
- [ ] 定期安全审查
- [ ] **CSP合规性检查**
  - [ ] 禁止使用外部CDN资源 (如React CDN)
  - [ ] 禁止内联脚本执行
  - [ ] 所有JavaScript必须来自本地文件
  - [ ] 使用原生DOM操作替代React组件

### 文档维护
- [ ] 更新开发文档
- [ ] 维护API文档
- [ ] 记录重要决策和变更
- [ ] 保持README文件更新

---

## 验收标准

### 功能验收
- [ ] 用户可以成功输入内容并选择平台
- [ ] 扩展可以自动打开目标平台页面
- [ ] 内容可以正确填充到各平台
- [ ] 发布状态可以实时反馈给用户
- [ ] 失败的发布可以重试

### 性能验收
- [ ] 扩展启动时间 < 2秒
- [ ] 单平台发布时间 < 30秒
- [ ] 内存使用 < 100MB
- [ ] CPU使用率 < 10%

### 稳定性验收
- [ ] 连续发布成功率 > 90%
- [ ] 异常恢复时间 < 5秒
- [ ] 24小时稳定运行无崩溃
- [ ] 支持至少10次连续发布

---

## 开发工具和资源

### 必备工具
- [ ] VS Code + 扩展插件
  - [ ] TypeScript Hero
  - [ ] Tailwind CSS IntelliSense
  - [ ] Chrome Extension Developer Tools
  - [ ] Playwright Test for VS Code
  - [ ] **CSP Validator** (检查内容安全策略合规性)
- [ ] Chrome 浏览器 (启用开发者模式)
- [ ] Git 版本控制
- [ ] Playwright MCP工具 (用于自动化测试)
- [ ] TypeScript 编译器
- [ ] Plasmo CLI (仅用于最终构建)
- [ ] **Chrome扩展CSP测试工具**

### 参考资源
- [ ] Chrome Extension 官方文档 (Manifest V3)
- [ ] TypeScript 官方文档
- [ ] React 18 官方文档
- [ ] Tailwind CSS 文档
- [ ] Playwright 自动化测试文档
- [ ] 各平台开发者文档
- [ ] Plasmo Framework 文档 (构建阶段参考)

## 风险控制和应急预案

### 技术风险
- **CSP (Content Security Policy) 违规**:
  - 风险: 使用外部CDN、内联脚本导致扩展无法加载
  - 预防: 严格使用本地资源，原生DOM操作
  - 应急: 准备纯JavaScript替代方案
- **平台页面结构变化**: 建立配置文件热更新机制
- **权限限制**: 准备备用权限申请方案
- **性能问题**: 实现渐进式加载和优化策略
- **兼容性问题**: 建立多版本测试环境
- **Manifest V3合规性**: 确保所有API调用符合最新规范

### 进度风险
- **开发延期**: 准备功能优先级调整方案
- **测试不充分**: 建立自动化测试流程
- **集成问题**: 提前进行模块间集成测试

### 质量风险
- **用户体验差**: 建立用户反馈收集机制
- **稳定性问题**: 实现完善的错误监控和日志系统
- **安全漏洞**: 定期进行安全审查和代码扫描

## 团队协作规范

### 代码规范
- [ ] 统一代码格式化配置 (Prettier)
- [ ] 统一代码检查规则 (ESLint)
- [ ] 统一提交信息格式 (Conventional Commits)
- [ ] 统一分支管理策略 (Git Flow)

### 沟通机制
- [ ] 每日站会 (15分钟)
- [ ] 周度进度回顾
- [ ] 重要决策文档化
- [ ] 技术难点及时讨论

### 质量保证
- [ ] 代码审查制度
- [ ] 功能测试检查清单
- [ ] 发布前质量门禁
- [ ] 用户反馈处理流程

## 发布和部署

### 版本管理
- [ ] 语义化版本号 (Semantic Versioning)
- [ ] 版本发布说明
- [ ] 回滚方案准备
- [ ] 用户升级指导

### 发布流程
- [ ] 内部测试版本
- [ ] Beta 版本发布
- [ ] 正式版本发布
- [ ] 应用商店上架

### 监控和维护
- [ ] 用户使用数据收集
- [ ] 错误日志监控
- [ ] 性能指标跟踪
- [ ] 用户反馈处理

---

**重要提醒**:
1. 每个任务完成后需要进行自测，重要功能需要交叉测试
2. 遇到技术难点及时沟通，确保项目按时高质量交付
3. 保持与产品需求的一致性，及时同步变更
4. 注重用户体验和代码质量，不追求快速但低质量的实现
5. 定期备份代码和文档，确保项目资产安全
