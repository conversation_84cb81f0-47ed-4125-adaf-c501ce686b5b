{"compilerOptions": {"target": "ES2020", "lib": ["ES2020", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "./dist", "rootDir": "./src", "jsx": "react-jsx", "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitThis": true, "noImplicitReturns": true, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/shared/*": ["./src/shared/*"], "@/popup/*": ["./src/popup/*"], "@/sidepanel/*": ["./src/sidepanel/*"], "@/background/*": ["./src/background/*"], "@/content-scripts/*": ["./src/content-scripts/*"]}, "types": ["chrome", "node"]}, "include": ["src/**/*", "popup/**/*", "sidepanel/**/*", "background/**/*", "content-scripts/**/*", "shared/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.spec.ts"]}