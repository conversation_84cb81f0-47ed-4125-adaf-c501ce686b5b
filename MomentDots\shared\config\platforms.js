// 统一的平台配置文件
// 避免在多个文件中重复定义平台信息

const SUPPORTED_PLATFORMS = [
  { 
    id: 'weibo', 
    name: '微博', 
    publishUrl: 'https://weibo.com/compose', 
    color: 'bg-red-500',
    user: '@用户756873663'
  },
  { 
    id: 'xiaoh<PERSON><PERSON>', 
    name: '小红书', 
    publishUrl: 'https://creator.xiaohongshu.com/publish/publish', 
    color: 'bg-red-500',
    user: '@viccdin_z'
  },
  { 
    id: 'jike', 
    name: '即刻', 
    publishUrl: 'https://web.okjike.com', 
    color: 'bg-yellow-500'
  },
  { 
    id: 'douyin', 
    name: '抖音', 
    publishUrl: 'https://creator.douyin.com/creator-micro/content/upload', 
    color: 'bg-black'
  }
];

// 根据ID获取平台信息
function getPlatformById(id) {
  return SUPPORTED_PLATFORMS.find(platform => platform.id === id);
}

// 根据ID数组获取平台列表
function getPlatformsByIds(ids) {
  return SUPPORTED_PLATFORMS.filter(platform => ids.includes(platform.id));
}

// 获取所有平台ID
function getAllPlatformIds() {
  return SUPPORTED_PLATFORMS.map(platform => platform.id);
}

// 验证平台ID是否有效
function isValidPlatformId(id) {
  return SUPPORTED_PLATFORMS.some(platform => platform.id === id);
}
