// 小红书平台适配器
console.log('小红书内容脚本已加载');

class XiaohongshuAdapter {
  constructor() {
    this.platform = 'xiaohongshu';
    this.selectors = {
      titleInput: 'input[placeholder*="填写标题"]',
      contentTextarea: 'div[contenteditable="true"]',
      publishButton: 'button:contains("发布笔记")',
      imageUpload: 'input[type="file"][accept*="image"]'
    };
  }

  async waitForElement(selector, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const check = () => {
        let element;
        
        // 处理包含文本的选择器
        if (selector.includes(':contains(')) {
          const text = selector.match(/:contains\("([^"]+)"\)/)[1];
          const tagName = selector.split(':')[0];
          const elements = document.querySelectorAll(tagName);
          element = Array.from(elements).find(el => el.textContent.includes(text));
        } else {
          element = document.querySelector(selector);
        }
        
        if (element) {
          resolve(element);
          return;
        }
        
        if (Date.now() - startTime > timeout) {
          reject(new Error(`Element ${selector} not found within ${timeout}ms`));
          return;
        }
        
        setTimeout(check, 100);
      };
      
      check();
    });
  }

  async fillContent(selector, content) {
    try {
      const element = await this.waitForElement(selector);
      
      if (element.tagName === 'INPUT') {
        element.value = '';
        element.focus();
        element.value = content;
        element.dispatchEvent(new Event('input', { bubbles: true }));
        element.dispatchEvent(new Event('change', { bubbles: true }));
      } else if (element.contentEditable === 'true') {
        element.focus();
        element.innerHTML = '';
        element.textContent = content;
        element.dispatchEvent(new Event('input', { bubbles: true }));
      }
      
      console.log('Content filled successfully');
      return true;
    } catch (error) {
      console.error('Failed to fill content:', error);
      throw error;
    }
  }

  async clickElement(selector) {
    try {
      const element = await this.waitForElement(selector);
      element.click();
      console.log('Element clicked successfully');
      return true;
    } catch (error) {
      console.error('Failed to click element:', error);
      throw error;
    }
  }

  async publish(data) {
    console.log('开始小红书发布流程:', data);
    
    try {
      const { title, content } = data;
      
      // 1. 等待页面加载完成
      await new Promise(resolve => setTimeout(resolve, 3000));
      console.log('小红书发布页面加载完成');
      
      // 2. 填充标题（如果有）
      if (title) {
        try {
          await this.fillContent(this.selectors.titleInput, title);
          console.log('标题填充完成');
        } catch (error) {
          console.log('标题输入框未找到，跳过标题填充');
        }
      }
      
      // 3. 填充内容
      await this.fillContent(this.selectors.contentTextarea, content);
      console.log('内容填充完成');
      
      // 4. 等待一下让页面处理
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // 5. 点击发布按钮
      await this.clickElement(this.selectors.publishButton);
      console.log('发布按钮已点击');
      
      // 6. 等待发布完成
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      console.log('小红书发布完成');
      
      return {
        success: true,
        message: '发布成功',
        url: window.location.href
      };
      
    } catch (error) {
      console.error('小红书发布失败:', error);
      return {
        success: false,
        message: error.message || '发布失败'
      };
    }
  }
}

// 创建适配器实例
const xiaohongshuAdapter = new XiaohongshuAdapter();

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('小红书内容脚本收到消息:', message);
  
  if (message.action === 'publish') {
    xiaohongshuAdapter.publish(message.data)
      .then(result => {
        console.log('小红书发布结果:', result);
        sendResponse(result);
      })
      .catch(error => {
        console.error('小红书发布错误:', error);
        sendResponse({
          success: false,
          message: error.message || '发布失败'
        });
      });
    
    return true; // 保持消息通道开放
  }
});

console.log('小红书适配器初始化完成');
