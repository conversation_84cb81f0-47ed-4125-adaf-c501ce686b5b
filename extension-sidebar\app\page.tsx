export default function ExtensionSidebar() {
  return (
    <div className="w-80 h-screen bg-gray-50 border-r border-gray-200 flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-3 bg-white border-b border-gray-200">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-green-500 rounded-sm flex items-center justify-center">
            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <span className="text-sm font-medium text-gray-800">助手</span>
        </div>
        <div className="flex items-center gap-1">
          <button className="w-6 h-6 flex items-center justify-center hover:bg-gray-100 rounded">
            <svg className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                clipRule="evenodd"
              />
            </svg>
          </button>
          <button className="w-6 h-6 flex items-center justify-center hover:bg-gray-100 rounded">
            <svg className="w-4 h-4 text-gray-600" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>

      {/* Description */}
      <div className="p-3 bg-blue-50 border-b border-blue-100">
        <p className="text-xs text-blue-700 leading-relaxed">
          如果网页被或未登录查询页面与步骤不一致，请点击图标旁的刷新按钮重新加载，同时请确保浏览器已正在某某综合
        </p>
      </div>

      {/* Options List */}
      <div className="flex-1 p-3 space-y-3">
        {/* Xiaohongshu Option */}
        <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 cursor-pointer">
          <input type="radio" name="platform" className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500" />
          <div className="w-8 h-8 bg-red-500 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">小红</span>
          </div>
          <span className="text-sm font-medium text-gray-800">小红书创作服务平台</span>
        </div>

        {/* Jike Option */}
        <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 cursor-pointer">
          <input type="radio" name="platform" className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500" />
          <div className="w-8 h-8 bg-yellow-400 rounded flex items-center justify-center">
            <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path
                fillRule="evenodd"
                d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <span className="text-sm font-medium text-gray-800">即刻</span>
        </div>

        {/* Zhihu Option */}
        <div className="flex items-center gap-3 p-3 bg-white rounded-lg border border-gray-200 hover:border-gray-300 cursor-pointer">
          <input type="radio" name="platform" className="w-4 h-4 text-blue-600 border-gray-300 focus:ring-blue-500" />
          <div className="w-8 h-8 bg-blue-500 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">知</span>
          </div>
          <span className="text-sm font-medium text-gray-800">蓝页 - 知乎</span>
        </div>
      </div>
    </div>
  )
}
