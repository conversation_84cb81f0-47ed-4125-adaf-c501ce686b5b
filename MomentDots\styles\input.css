@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义基础样式 */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  html, body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
  }
  
  button {
    cursor: pointer;
    transition: all 0.2s ease-in-out;
  }
  
  input, textarea {
    transition: all 0.2s ease-in-out;
  }
  
  input:focus, textarea:focus {
    outline: none;
    ring: 2px;
    ring-color: rgb(59 130 246);
  }
}

/* 自定义组件样式 */
@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent;
  }
  
  .textarea-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none;
  }
  
  .platform-card {
    @apply border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors duration-200 cursor-pointer;
  }
  
  .platform-card.selected {
    @apply border-blue-500 bg-blue-50;
  }
  
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-pending {
    @apply bg-gray-100 text-gray-800;
  }
  
  .status-publishing {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .status-success {
    @apply bg-green-100 text-green-800;
  }
  
  .status-failed {
    @apply bg-red-100 text-red-800;
  }
}

/* 自定义工具样式 */
@layer utilities {
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}
