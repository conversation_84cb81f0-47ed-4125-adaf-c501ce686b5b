<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>动态发布助手</title>
  <link href="../styles/output.css" rel="stylesheet">
  <style>
    body {
      margin: 0;
      padding: 0;
      min-height: 100vh;
      background-color: #f9fafb;
      font-family: 'Inter', system-ui, -apple-system, sans-serif;
    }
    
    .main-container {
      min-height: 100vh;
      display: flex;
      flex-direction: column;
    }
    
    .content-area {
      flex: 1;
      max-width: 1200px;
      margin: 0 auto;
      padding: 2rem;
      width: 100%;
    }
    
    .form-grid {
      display: grid;
      grid-template-columns: 1fr 400px;
      gap: 2rem;
      min-height: 600px;
    }
    
    @media (max-width: 1024px) {
      .form-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
      }
      
      .content-area {
        padding: 1rem;
      }
    }
    
    /* 自定义滚动条 */
    ::-webkit-scrollbar {
      width: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    
    ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
    
    /* 加载动画 */
    .loading-spinner {
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  </style>
</head>
<body>
  <div id="main-root" class="main-container">
    <!-- 加载中状态 -->
    <div class="flex items-center justify-center min-h-screen">
      <div class="text-center">
        <div class="loading-spinner w-8 h-8 border-4 border-blue-200 border-t-blue-600 rounded-full mx-auto mb-4"></div>
        <p class="text-gray-500">加载中...</p>
      </div>
    </div>
  </div>
  
  <!-- 应用脚本 -->
  <script src="../shared/config/platforms.js"></script>
  <script src="../shared/utils/storage.js"></script>
  <script src="main.js"></script>
</body>
</html>
