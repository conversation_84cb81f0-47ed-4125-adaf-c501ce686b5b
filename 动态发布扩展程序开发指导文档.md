# 动态发布扩展程序开发指导文档

## 项目概述

开发一个专注于"动态发布"功能的Chrome浏览器扩展程序，实现微博、小红书、即刻、抖音四个平台的自动化动态发布功能。

### 用户流程设计
1. **第一阶段**：用户点击扩展程序图标 → 打开完整的内容输入页面（基于 `content-sync-app`）
2. **第二阶段**：用户点击"开始同步" → 进入发布阶段，显示侧边栏（基于 `extension-sidebar`）

### 两个核心页面
- **主页面** (`content-sync-app`): 完整的内容输入和平台选择界面
- **侧边栏** (`extension-sidebar`): 发布过程中的状态监控界面

## 技术架构

### 核心技术栈
- **框架**: 原生Chrome扩展 (开发阶段) + Plasmo Framework (仅用于最终打包)
- **前端**: 原生JavaScript/TypeScript + DOM操作 (避免CSP违规)
- **UI库**: Tailwind CSS + 自定义组件 (不使用React相关库)
- **状态管理**: Zustand (轻量级状态管理) + Chrome Storage API
- **构建工具**: TypeScript编译器 + Plasmo (仅最终构建阶段)
- **重要**: 严格遵循Chrome扩展CSP (Content Security Policy) 规范

### 项目结构
```
MomentDots/                     # 扩展程序主目录
├── src/
│   ├── popup/                  # 主页面 (基于content-sync-app)
│   │   ├── index.tsx           # 内容输入页面
│   │   ├── components/         # 复用content-sync-app的UI组件
│   │   └── stores/             # 状态管理
│   ├── sidepanel/              # 侧边栏 (基于extension-sidebar)
│   │   ├── index.tsx           # 发布状态监控页面
│   │   ├── components/         # 状态显示组件
│   │   └── stores/             # 状态管理
│   ├── background/             # 后台服务
│   │   ├── index.ts            # Service Worker入口
│   │   ├── task-scheduler.ts   # 任务调度器
│   │   └── tab-manager.ts      # 标签页管理
│   ├── content-scripts/        # 内容脚本
│   │   ├── adapters/
│   │   │   ├── weibo.ts        # 微博适配器
│   │   │   ├── xiaohongshu.ts  # 小红书适配器
│   │   │   ├── jike.ts         # 即刻适配器
│   │   │   └── douyin.ts       # 抖音适配器
│   │   └── shared/
│   │       ├── base-adapter.ts # 基础适配器类
│   │       └── utils.ts        # 工具函数
│   ├── configs/               # 平台配置
│   │   └── platforms/
│   │       ├── weibo.json
│   │       ├── xiaohongshu.json
│   │       ├── jike.json
│   │       └── douyin.json
│   └── shared/               # 共享模块
│       ├── types/            # TypeScript类型定义
│       ├── constants/        # 常量定义
│       └── utils/            # 工具函数
├── assets/                   # 静态资源
├── manifest.json            # 扩展配置
└── package.json
```

## 核心功能设计

### 1. 主页面 (Popup) - 第一阶段

基于现有的 `content-sync-app/app/page.tsx`，作为扩展的主入口页面：

**功能特点：**
- 完整的内容输入界面（标题、内容、图片上传）
- 平台选择：微博、小红书、即刻、抖音
- "开始同步"按钮触发发布流程
- 保持原有的UI设计和交互体验

### 2. 侧边栏界面 (SidePanel) - 第二阶段

基于现有的 `extension-sidebar/app/page.tsx`，在发布过程中显示：

**功能特点：**
- 显示各平台发布状态（等待中、发布中、成功、失败）
- 实时更新发布进度
- 支持失败平台的重试操作
- 紧凑的侧边栏设计，不干扰主页面操作

**核心数据结构：**
```typescript
interface DynamicPublishForm {
  title: string;           // 动态标题
  content: string;         // 动态内容
  images: File[];          // 图片文件
  selectedPlatforms: Platform[]; // 选中的平台
}

interface PublishStatus {
  platform: Platform;
  status: 'pending' | 'publishing' | 'success' | 'failed';
  message?: string;
  publishUrl?: string;     // 发布成功后的链接
}
```

### 2. 配置驱动的平台适配

每个平台使用独立的JSON配置文件：

```json
// configs/platforms/weibo.json
{
  "name": "微博",
  "publishUrl": "https://weibo.com/compose",
  "selectors": {
    "contentTextarea": "textarea[placeholder*='有什么新鲜事想告诉大家']",
    "imageUpload": "input[type='file'][accept*='image']",
    "publishButton": "button[action-type='submit']"
  },
  "actions": [
    {
      "type": "wait",
      "selector": "textarea[placeholder*='有什么新鲜事想告诉大家']",
      "timeout": 5000
    },
    {
      "type": "fill",
      "selector": "textarea[placeholder*='有什么新鲜事想告诉大家']",
      "value": "{content}"
    },
    {
      "type": "upload",
      "selector": "input[type='file'][accept*='image']",
      "files": "{images}"
    },
    {
      "type": "click",
      "selector": "button[action-type='submit']"
    }
  ]
}
```

### 3. 基础适配器类

```typescript
abstract class BasePlatformAdapter {
  protected config: PlatformConfig;
  
  constructor(config: PlatformConfig) {
    this.config = config;
  }
  
  abstract async publish(content: PublishContent): Promise<PublishResult>;
  
  protected async waitForElement(selector: string, timeout = 10000): Promise<Element> {
    // 智能等待元素出现
  }
  
  protected async fillContent(selector: string, content: string): Promise<void> {
    // 填充文本内容
  }
  
  protected async uploadImages(selector: string, images: File[]): Promise<void> {
    // 上传图片
  }
  
  protected async clickElement(selector: string): Promise<void> {
    // 点击元素
  }
}
```

### 4. 任务调度系统

```typescript
class TaskScheduler {
  private maxConcurrency = 2; // 最大并发数（避免浏览器压力过大）
  
  async executeTasks(platforms: Platform[], content: PublishContent): Promise<void> {
    const tasks = platforms.map(platform => ({
      platform,
      execute: () => this.executeTask(platform, content)
    }));
    
    // 分批执行，控制并发
    const chunks = this.chunkArray(tasks, this.maxConcurrency);
    for (const chunk of chunks) {
      await Promise.allSettled(
        chunk.map(task => task.execute())
      );
    }
  }
  
  private async executeTask(platform: Platform, content: PublishContent): Promise<void> {
    // 1. 打开平台发布页面
    const tab = await chrome.tabs.create({ 
      url: platform.publishUrl, 
      active: false 
    });
    
    // 2. 注入内容脚本
    await chrome.scripting.executeScript({
      target: { tabId: tab.id! },
      files: [`content-scripts/${platform.id}.js`]
    });
    
    // 3. 发送发布指令
    await chrome.tabs.sendMessage(tab.id!, {
      action: 'publish',
      content
    });
  }
}
```

## 开发阶段规划

### 第一阶段：基础框架搭建 (Week 1-2)

**目标：** 建立项目基础架构和两个核心页面

**任务清单：**
- [ ] 在 `MomentDots` 目录使用 Plasmo 初始化项目
- [ ] 配置 TypeScript 和 Tailwind CSS
- [ ] 从 `content-sync-app` 迁移主页面UI组件到 `popup`
- [ ] 从 `extension-sidebar` 迁移侧边栏UI组件到 `sidepanel`
- [ ] 建立两个页面之间的消息通信机制
- [ ] 配置 manifest.json 支持 popup 和 sidepanel

**验收标准：**
- 点击扩展图标能正常显示主页面（内容输入界面）
- 主页面的表单输入功能正常，平台选择功能可用
- 点击"开始同步"能触发侧边栏显示
- 侧边栏能正确显示平台状态信息

### 第二阶段：核心平台适配 (Week 3-5)

**目标：** 实现四个核心平台的自动化发布

**任务清单：**
- [ ] 实现微博动态发布适配器
- [ ] 实现小红书笔记发布适配器  
- [ ] 实现即刻动态发布适配器
- [ ] 实现抖音动态发布适配器
- [ ] 建立配置文件管理系统

**验收标准：**
- 每个平台都能成功发布纯文本动态
- 支持图片上传功能
- 基础的错误处理机制

### 第三阶段：任务调度和状态管理 (Week 6-7)

**目标：** 实现并行发布和状态监控

**任务清单：**
- [ ] 实现任务调度器
- [ ] 建立状态反馈系统
- [ ] 添加发布进度显示
- [ ] 实现失败重试机制

**验收标准：**
- 支持多平台并行发布
- 实时显示发布状态
- 失败任务可以单独重试

### 第四阶段：优化和测试 (Week 8)

**目标：** 完善用户体验和稳定性

**任务清单：**
- [ ] 异常处理优化
- [ ] 用户界面优化
- [ ] 性能优化
- [ ] 全面测试

**验收标准：**
- 发布成功率 > 90%
- 用户界面流畅无卡顿
- 异常情况有明确提示

## 关键技术要点

### 1. Manifest V3 配置

```json
{
  "manifest_version": 3,
  "name": "动态发布助手",
  "version": "1.0.0",
  "description": "一键发布动态到微博、小红书、即刻、抖音",
  "permissions": [
    "storage",
    "scripting",
    "sidePanel",
    "tabs",
    "activeTab"
  ],
  "host_permissions": [
    "https://weibo.com/*",
    "https://www.xiaohongshu.com/*",
    "https://web.okjike.com/*",
    "https://creator.douyin.com/*"
  ],
  "background": {
    "service_worker": "background.js"
  },
  "action": {
    "default_popup": "popup.html",
    "default_title": "动态发布助手"
  },
  "side_panel": {
    "default_path": "sidepanel.html"
  },
  "icons": {
    "16": "assets/icon16.png",
    "32": "assets/icon32.png",
    "48": "assets/icon48.png",
    "128": "assets/icon128.png"
  }
}
```

### 2. 智能等待机制

```typescript
class SmartWaiter {
  async waitForElement(selector: string, timeout = 10000): Promise<Element> {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const check = () => {
        const element = document.querySelector(selector);
        if (element) {
          resolve(element);
          return;
        }
        
        if (Date.now() - startTime > timeout) {
          reject(new Error(`Element ${selector} not found within ${timeout}ms`));
          return;
        }
        
        setTimeout(check, 100);
      };
      
      check();
    });
  }
  
  async waitForNetworkIdle(timeout = 5000): Promise<void> {
    // 等待网络请求完成
    return new Promise(resolve => {
      let timer: NodeJS.Timeout;
      
      const resetTimer = () => {
        clearTimeout(timer);
        timer = setTimeout(resolve, 1000);
      };
      
      // 监听网络活动
      const observer = new PerformanceObserver(resetTimer);
      observer.observe({ entryTypes: ['resource'] });
      
      resetTimer();
      
      setTimeout(() => {
        observer.disconnect();
        resolve();
      }, timeout);
    });
  }
}
```

### 3. 错误处理和重试

```typescript
class RetryHandler {
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    maxRetries = 3,
    delay = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        
        if (i === maxRetries) break;
        
        // 指数退避
        await this.sleep(delay * Math.pow(2, i));
      }
    }
    
    throw lastError!;
  }
  
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

## 安全和合规考虑

1. **最小权限原则**: 只请求必要的权限
2. **数据安全**: 用户输入的内容仅在本地处理，不上传到服务器
3. **隐私保护**: 不收集用户的登录信息或个人数据
4. **平台合规**: 遵守各平台的使用条款，模拟真实用户行为

## 后续扩展计划

1. **更多平台支持**: 根据用户需求添加更多平台
2. **定时发布**: 支持预定时间发布
3. **内容模板**: 提供常用内容模板
4. **数据统计**: 发布效果统计和分析

## 详细实现指南

### 1. 项目初始化步骤 (CSP兼容方式)

```bash
# 1. 创建标准Chrome扩展目录结构 (不使用Plasmo CLI)
cd MomentDots
mkdir -p popup sidepanel background content-scripts shared styles assets

# 2. 安装必要依赖 (避免React相关依赖)
npm init -y
npm install zustand tailwindcss typescript --save-dev
npm install @types/chrome --save-dev

# 3. 配置TypeScript编译环境
# 创建 tsconfig.json (见配置示例)

# 4. 手动创建HTML文件 (不使用JSX)
# popup/popup.html - 主页面
# sidepanel/sidepanel.html - 侧边栏

# 5. 使用原生JavaScript/TypeScript编写逻辑
# popup/popup.js - 原生DOM操作
# sidepanel/sidepanel.js - 原生DOM操作

# 6. 手动编写manifest.json (Manifest V3规范)

# 重要提醒:
# - 不使用外部CDN资源
# - 不使用内联脚本
# - 所有代码必须在本地文件中
```

### 2. 核心类型定义

```typescript
// src/shared/types/index.ts
export interface Platform {
  id: 'weibo' | 'xiaohongshu' | 'jike' | 'douyin';
  name: string;
  publishUrl: string;
  color: string;
  user?: string;
}

export interface PublishContent {
  title?: string;
  content: string;
  images: File[];
  platforms: Platform[];
}

export interface PublishResult {
  platform: Platform;
  status: 'success' | 'failed';
  message?: string;
  publishUrl?: string;
}

export interface PlatformConfig {
  name: string;
  publishUrl: string;
  selectors: {
    [key: string]: string;
  };
  actions: ActionStep[];
  waitConditions?: WaitCondition[];
}

export interface ActionStep {
  type: 'wait' | 'fill' | 'click' | 'upload' | 'scroll';
  selector: string;
  value?: string;
  timeout?: number;
  optional?: boolean;
}

export interface WaitCondition {
  type: 'element' | 'network' | 'time';
  selector?: string;
  timeout: number;
}
```

### 3. 状态管理 (Zustand Store)

```typescript
// src/sidepanel/stores/publish-store.ts
import { create } from 'zustand';
import { PublishContent, PublishResult, Platform } from '@/shared/types';

interface PublishState {
  // 表单数据
  title: string;
  content: string;
  images: File[];
  selectedPlatforms: Platform[];

  // 发布状态
  isPublishing: boolean;
  publishResults: PublishResult[];

  // 操作方法
  setTitle: (title: string) => void;
  setContent: (content: string) => void;
  setImages: (images: File[]) => void;
  togglePlatform: (platform: Platform) => void;
  startPublish: () => void;
  updatePublishResult: (result: PublishResult) => void;
  reset: () => void;
}

export const usePublishStore = create<PublishState>((set, get) => ({
  // 初始状态
  title: '',
  content: '',
  images: [],
  selectedPlatforms: [],
  isPublishing: false,
  publishResults: [],

  // 操作方法
  setTitle: (title) => set({ title }),
  setContent: (content) => set({ content }),
  setImages: (images) => set({ images }),

  togglePlatform: (platform) => set((state) => ({
    selectedPlatforms: state.selectedPlatforms.find(p => p.id === platform.id)
      ? state.selectedPlatforms.filter(p => p.id !== platform.id)
      : [...state.selectedPlatforms, platform]
  })),

  startPublish: async () => {
    const { title, content, images, selectedPlatforms } = get();

    set({ isPublishing: true, publishResults: [] });

    // 发送消息到后台脚本
    chrome.runtime.sendMessage({
      action: 'startPublish',
      data: { title, content, images, platforms: selectedPlatforms }
    });
  },

  updatePublishResult: (result) => set((state) => ({
    publishResults: [...state.publishResults.filter(r => r.platform.id !== result.platform.id), result]
  })),

  reset: () => set({
    title: '',
    content: '',
    images: [],
    selectedPlatforms: [],
    isPublishing: false,
    publishResults: []
  })
}));
```

### 4. 主页面组件 (Popup) - CSP兼容版本

```html
<!-- popup/popup.html - 符合CSP规范的HTML结构 -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>动态发布助手</title>
  <link href="../styles/output.css" rel="stylesheet">
</head>
<body>
  <div id="popup-root" class="w-full h-full bg-gray-50">
    <!-- 内容将由原生JavaScript渲染 -->
  </div>
  <!-- ✅ 正确: 使用本地JavaScript文件 -->
  <script src="popup.js"></script>
</body>
</html>
```

```javascript
// popup/popup.js - 使用原生DOM操作 (CSP兼容)
console.log('Popup script loaded');

const SUPPORTED_PLATFORMS = [
  { id: 'weibo', name: '微博', publishUrl: 'https://weibo.com/compose', color: 'bg-red-500' },
  { id: 'xiaohongshu', name: '小红书', publishUrl: 'https://creator.xiaohongshu.com/publish/publish', color: 'bg-red-500' },
  { id: 'jike', name: '即刻', publishUrl: 'https://web.okjike.com', color: 'bg-yellow-500' },
  { id: 'douyin', name: '抖音', publishUrl: 'https://creator.douyin.com/creator-micro/content/upload', color: 'bg-black' }
];

// ✅ 使用原生DOM操作替代React组件
function createPageContent() {
  const root = document.getElementById('popup-root');
  root.innerHTML = `
    <div class="w-[600px] h-[700px] bg-gray-50">
      <!-- Header -->
      <div class="bg-white border-b">
        <div class="px-4 py-4">
          <h1 class="text-xl font-medium text-center text-gray-800">动态发布助手</h1>
        </div>
      </div>

      <!-- Content -->
      <div class="p-4">
        <div class="grid grid-cols-2 gap-6">
          <div id="publish-form"></div>
          <div id="platform-selector"></div>
        </div>
      </div>
    </div>
  `;
}

async function handleStartPublish() {
  try {
    // 开始发布流程
    await chrome.runtime.sendMessage({ action: 'startPublish', data: {...} });

    // 打开侧边栏
    await chrome.sidePanel.open({ windowId: chrome.windows.WINDOW_ID_CURRENT });

    // 关闭popup
    window.close();
  } catch (error) {
    console.error('Failed to start publish:', error);
  }
}

// 页面初始化
document.addEventListener('DOMContentLoaded', () => {
  createPageContent();
  // 绑定事件监听器...
});
```

### 5. 侧边栏组件 (SidePanel)

```typescript
// src/sidepanel/index.tsx
import React, { useEffect } from 'react';
import { usePublishStore } from '../shared/stores/publish-store';
import { PublishStatusList } from './components/PublishStatusList';

export default function SidePanel() {
  const { publishResults, updatePublishResult } = usePublishStore();

  useEffect(() => {
    // 监听后台脚本的发布状态更新
    const handleMessage = (message: any) => {
      if (message.action === 'publishResult') {
        updatePublishResult(message.data);
      }
    };

    chrome.runtime.onMessage.addListener(handleMessage);

    return () => {
      chrome.runtime.onMessage.removeListener(handleMessage);
    };
  }, [updatePublishResult]);

  return (
    <div className="w-80 h-screen bg-gray-50 border-r border-gray-200 flex flex-col">
      {/* Header - 基于 extension-sidebar 设计 */}
      <div className="flex items-center justify-between p-3 bg-white border-b border-gray-200">
        <div className="flex items-center gap-2">
          <div className="w-4 h-4 bg-green-500 rounded-sm flex items-center justify-center">
            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
            </svg>
          </div>
          <span className="text-sm font-medium text-gray-800">发布助手</span>
        </div>
      </div>

      {/* Description */}
      <div className="p-3 bg-blue-50 border-b border-blue-100">
        <p className="text-xs text-blue-700 leading-relaxed">
          正在自动发布到选中的平台，请保持页面打开直到发布完成
        </p>
      </div>

      {/* Platform Status List */}
      <div className="flex-1 p-3">
        <PublishStatusList results={publishResults} />
      </div>
    </div>
  );
}
```

### 5. 平台适配器实现示例

```typescript
// src/content-scripts/adapters/weibo.ts
import { BasePlatformAdapter } from '../shared/base-adapter';
import { PublishContent, PublishResult } from '@/shared/types';

export class WeiboAdapter extends BasePlatformAdapter {
  async publish(content: PublishContent): Promise<PublishResult> {
    try {
      // 1. 等待页面加载完成
      await this.waitForElement('textarea[placeholder*="有什么新鲜事想告诉大家"]', 10000);

      // 2. 填充内容
      const contentText = content.title ? `${content.title}\n${content.content}` : content.content;
      await this.fillContent('textarea[placeholder*="有什么新鲜事想告诉大家"]', contentText);

      // 3. 上传图片（如果有）
      if (content.images.length > 0) {
        await this.uploadImages('input[type="file"][accept*="image"]', content.images);
        // 等待图片上传完成
        await this.waitForNetworkIdle();
      }

      // 4. 点击发布按钮
      await this.clickElement('button[action-type="submit"]');

      // 5. 等待发布完成
      await this.waitForElement('.m-layer-success', 5000);

      return {
        platform: { id: 'weibo', name: '微博', publishUrl: this.config.publishUrl, color: 'bg-red-500' },
        status: 'success',
        message: '发布成功'
      };

    } catch (error) {
      return {
        platform: { id: 'weibo', name: '微博', publishUrl: this.config.publishUrl, color: 'bg-red-500' },
        status: 'failed',
        message: error.message
      };
    }
  }
}
```

### 6. 后台脚本实现

```typescript
// src/background/index.ts
import { TaskScheduler } from './task-scheduler';
import { PublishContent } from '@/shared/types';

const taskScheduler = new TaskScheduler();

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.action === 'startPublish') {
    handlePublishRequest(message.data);
  }
});

async function handlePublishRequest(content: PublishContent) {
  try {
    await taskScheduler.executeTasks(content.platforms, content);
  } catch (error) {
    console.error('发布任务执行失败:', error);
  }
}

// 监听标签页更新，确保内容脚本正确注入
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // 检查是否是支持的平台
    const supportedDomains = [
      'weibo.com',
      'xiaohongshu.com',
      'okjike.com',
      'douyin.com'
    ];

    const isSupported = supportedDomains.some(domain => tab.url!.includes(domain));

    if (isSupported) {
      // 注入对应的内容脚本
      const platform = getPlatformFromUrl(tab.url);
      if (platform) {
        chrome.scripting.executeScript({
          target: { tabId },
          files: [`content-scripts/${platform}.js`]
        });
      }
    }
  }
});

function getPlatformFromUrl(url: string): string | null {
  if (url.includes('weibo.com')) return 'weibo';
  if (url.includes('xiaohongshu.com')) return 'xiaohongshu';
  if (url.includes('okjike.com')) return 'jike';
  if (url.includes('douyin.com')) return 'douyin';
  return null;
}
```

### 7. 开发和调试指南

#### 开发环境设置
```bash
# 启动开发模式
npm run dev

# 构建生产版本
npm run build

# 打包扩展
npm run package
```

#### 调试技巧
1. **后台脚本调试**: 在 Chrome 扩展管理页面点击"检查视图"
2. **内容脚本调试**: 在目标页面按 F12，在 Console 中查看日志
3. **侧边栏调试**: 右键侧边栏选择"检查"

#### 常见问题解决
1. **权限问题**: 确保 manifest.json 中的 host_permissions 包含目标域名
2. **元素选择器失效**: 使用浏览器开发者工具重新获取正确的选择器
3. **异步操作超时**: 适当增加等待时间，添加重试机制

## 完整工作流程

### 用户操作流程
1. **启动阶段**: 用户点击扩展图标 → 打开主页面 (popup)
2. **内容输入**: 在主页面填写标题、内容、上传图片
3. **平台选择**: 勾选要发布的平台（微博、小红书、即刻、抖音）
4. **开始发布**: 点击"开始同步"按钮
5. **状态监控**: 自动打开侧边栏，显示各平台发布状态
6. **完成确认**: 查看发布结果，处理失败的平台

### 技术实现流程
1. **Popup 页面**: 收集用户输入，存储到 Chrome Storage
2. **后台脚本**: 接收发布指令，创建目标平台标签页
3. **内容脚本**: 注入到各平台页面，执行自动化操作
4. **侧边栏**: 实时显示发布状态，支持重试操作
5. **状态同步**: 通过消息传递机制同步各组件状态

### 关键技术点
- **页面间通信**: 使用 Chrome Runtime API 进行消息传递
- **状态管理**: 使用 Zustand + Chrome Storage 持久化状态
- **并发控制**: 限制同时打开的标签页数量，避免浏览器压力
- **错误处理**: 完善的重试机制和用户反馈

## 开发注意事项

### 1. CSP (Content Security Policy) 合规性 ⚠️
Chrome扩展有严格的内容安全策略，必须遵循以下规则：

**禁止事项:**
- ❌ 使用外部CDN资源 (如 `https://unpkg.com/react@18/...`)
- ❌ 内联脚本执行 (`<script>` 标签内的代码)
- ❌ 动态代码执行 (`eval()`, `new Function()`)
- ❌ 外部样式表引用

**必须做法:**
- ✅ 所有JavaScript文件必须本地化
- ✅ 使用原生DOM操作替代React组件
- ✅ 所有资源通过相对路径引用
- ✅ 在manifest.json中正确声明所有资源

**常见CSP错误及解决方案:**
```javascript
// ❌ 错误: 使用外部CDN
<script src="https://unpkg.com/react@18/umd/react.development.js"></script>

// ✅ 正确: 使用本地文件和原生DOM
<script src="popup.js"></script>
// 在popup.js中使用 document.createElement() 等原生API
```

### 2. 权限管理
- 确保 manifest.json 中的 host_permissions 包含所有目标平台
- 使用最小权限原则，避免申请不必要的权限

### 2. 用户体验
- 主页面保持与 content-sync-app 一致的设计风格
- 侧边栏保持与 extension-sidebar 一致的紧凑布局
- 提供清晰的状态反馈和错误提示

### 3. 平台适配
- 各平台的页面结构可能随时变化，需要建立监控机制
- 使用配置文件管理选择器，便于快速更新
- 实现智能等待机制，适应不同的网络环境

### 4. 测试策略
- 单元测试：测试各个组件的功能
- 集成测试：测试完整的发布流程
- 兼容性测试：确保在不同浏览器版本中正常工作
- **CSP合规性测试：确保所有资源加载符合内容安全策略**

### 5. CSP最佳实践和常见问题

#### 5.1 开发阶段CSP检查清单
- [ ] 所有JavaScript文件都在本地
- [ ] 没有使用外部CDN资源
- [ ] 没有内联脚本 (`<script>` 标签内的代码)
- [ ] 没有使用 `eval()` 或 `new Function()`
- [ ] 所有事件监听器通过 `addEventListener` 绑定
- [ ] 使用原生DOM操作替代框架组件

#### 5.2 常见CSP错误及解决方案

**错误1: 外部CDN资源**
```html
<!-- ❌ 错误 -->
<script src="https://unpkg.com/react@18/umd/react.development.js"></script>

<!-- ✅ 正确 -->
<script src="popup.js"></script>
```

**错误2: 内联脚本**
```html
<!-- ❌ 错误 -->
<button onclick="handleClick()">点击</button>

<!-- ✅ 正确 -->
<button id="my-button">点击</button>
<script src="popup.js"></script>
<!-- 在popup.js中: document.getElementById('my-button').addEventListener('click', handleClick); -->
```

**错误3: 动态代码执行**
```javascript
// ❌ 错误
eval('console.log("hello")');
new Function('return 1 + 1')();

// ✅ 正确
console.log("hello");
const result = 1 + 1;
```

#### 5.3 CSP调试技巧
1. 打开Chrome DevTools Console查看CSP错误
2. 错误信息会明确指出违规的资源或代码
3. 使用Chrome扩展开发者工具检查资源加载
4. 在manifest.json中不要添加自定义CSP策略（使用默认的更安全）

## 后续扩展计划

1. **更多平台支持**: 根据用户需求添加更多平台
2. **定时发布**: 支持预定时间发布
3. **内容模板**: 提供常用内容模板
4. **数据统计**: 发布效果统计和分析
5. **批量管理**: 支持多条内容的批量发布

---

本文档将作为项目开发的核心指导，所有开发工作都应基于此文档进行。请严格按照文档中的架构设计和实现方案执行开发任务。
