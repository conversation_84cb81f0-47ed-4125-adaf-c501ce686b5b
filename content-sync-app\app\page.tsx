"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Card } from "@/components/ui/card"
import { ImageIcon, VideoIcon, FileIcon, Send } from "lucide-react"

export default function ContentSyncPage() {
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([])
  const [title, setTitle] = useState("")
  const [content, setContent] = useState("")

  const platforms = [
    { id: "quanmin", name: "全站", color: "bg-gray-500" },
    { id: "weibo", name: "微博", color: "bg-red-500", user: "@用户756873663" },
    { id: "xiaohongshu", name: "小红书", color: "bg-red-500", user: "@viccdin_z" },
    { id: "zhihu", name: "知乎", color: "bg-blue-500", user: "@小编哥" },
    { id: "wechat", name: "微信公众号(图文)", color: "bg-green-500" },
    { id: "toutiao", name: "头条号(图文)", color: "bg-blue-500" },
    { id: "baijia", name: "百家号", color: "bg-green-500", user: "@21G" },
    { id: "baijiahao", name: "百家号", color: "bg-blue-500" },
    { id: "qutoutiao", name: "趣头条", color: "bg-blue-500" },
    { id: "jinri", name: "即时", color: "bg-yellow-500" },
    { id: "zhihu2", name: "知乎提醒", color: "bg-cyan-500" },
    { id: "zhihu3", name: "知乎导读", color: "bg-blue-500", user: "@小编哥" },
    { id: "zhushou", name: "助手(图文)", color: "bg-orange-500" },
    { id: "kuaishou", name: "快手(图文)", color: "bg-orange-500" },
    { id: "weixiao", name: "微笑条", color: "bg-red-500" },
    { id: "baijiahao2", name: "百家", color: "bg-blue-500", user: "@金融圈Cknight" },
    { id: "deli", name: "得到", color: "bg-orange-500" },
    { id: "aiqicha", name: "爱企查", color: "bg-cyan-500" },
  ]

  const handlePlatformToggle = (platformId: string) => {
    setSelectedPlatforms((prev) =>
      prev.includes(platformId) ? prev.filter((id) => id !== platformId) : [...prev, platformId],
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4 py-4">
          <h1 className="text-xl font-medium text-center text-gray-800">爱贝壳内容同步助手</h1>
          <p className="text-sm text-gray-500 text-center mt-2">
            {""}
          </p>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white border-b">
        <div className="max-w-6xl mx-auto px-4">
          <div className="flex space-x-8">
            <button className="py-4 px-2 border-b-2 border-blue-500 text-blue-600 font-medium">动态</button>
            <button className="py-4 px-2 text-gray-500 hover:text-gray-700">文章</button>
            <button className="py-4 px-2 text-gray-500 hover:text-gray-700">短视频</button>
            <button className="py-4 px-2 text-gray-500 hover:text-gray-700">播客</button>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Content Form */}
          <div className="lg:col-span-2">
            <Card className="p-6">
              <div className="space-y-4">
                {/* Title Input */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">标题</label>
                  <Input value={title} onChange={(e) => setTitle(e.target.value)} className="w-full" />
                </div>

                {/* Content Textarea */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">内容</label>
                  <Textarea
                    value={content}
                    onChange={(e) => setContent(e.target.value)}
                    className="w-full h-32 resize-none"
                    placeholder=""
                  />
                </div>

                {/* Uploaded Image */}
                <div className="mt-4">
                  <img
                    src="/cartoon-characters-illustration.png"
                    alt="Uploaded content"
                    className="w-30 h-30 object-cover rounded-lg border"
                  />
                </div>

                {/* Media Upload Buttons */}
                <div className="flex space-x-4 mt-4">
                  <Button variant="outline" size="sm" className="flex items-center space-x-2 bg-transparent">
                    <ImageIcon className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center space-x-2 bg-transparent">
                    <VideoIcon className="w-4 h-4" />
                  </Button>
                  <Button variant="outline" size="sm" className="flex items-center space-x-2 bg-transparent">
                    <FileIcon className="w-4 h-4" />
                  </Button>
                </div>

                {/* Sync Button */}
                <div className="flex justify-end mt-6">
                  <Button className="bg-gray-800 hover:bg-gray-900 text-white px-6">
                    <Send className="w-4 h-4 mr-2" />
                    开始同步
                  </Button>
                </div>
              </div>
            </Card>

            {/* Platform Selection Info */}
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <p className="text-sm text-gray-600">开启直接发布(否则需要手动点击发布)</p>
            </div>
          </div>

          {/* Platform Selection */}
          <div className="lg:col-span-1">
            <Card className="p-6">
              <div className="space-y-4">
                {platforms.map((platform) => (
                  <div key={platform.id} className="flex items-center space-x-3">
                    <Checkbox
                      id={platform.id}
                      checked={selectedPlatforms.includes(platform.id)}
                      onCheckedChange={() => handlePlatformToggle(platform.id)}
                    />
                    <div className="flex items-center space-x-2 flex-1">
                      
                      <span className="text-sm font-medium text-gray-700">{platform.name}</span>
                      {platform.user && <span className="text-xs text-gray-500">{platform.user}</span>}
                      <span className="text-xs text-gray-400 ml-auto">{""}</span>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
