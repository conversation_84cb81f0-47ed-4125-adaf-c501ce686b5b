// 动态发布助手 - 主页面 (新标签页模式)
console.log('Main page script loaded');

// 注意：将在页面加载后动态加载平台配置和存储工具

// 应用状态
let appState = {
  title: '',
  content: '',
  selectedPlatforms: [],
  imagePreviews: [], // 改为数组支持多图片
  isPublishing: false
};

// 图片上传配置
const IMAGE_CONFIG = {
  maxImages: 9,
  maxFileSize: 5 * 1024 * 1024, // 5MB
  allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
};

// 数据管理函数 - 使用统一的存储工具
async function loadFromStorageData() {
  try {
    const data = await loadPublishData();
    appState.title = data.title;
    appState.content = data.content;
    appState.selectedPlatforms = data.selectedPlatforms;
    appState.imagePreviews = data.imagePreviews || []; // 支持多图片数据
    updateUI();
  } catch (error) {
    console.error('Failed to load from storage:', error);
  }
}

async function saveToStorageData() {
  try {
    await savePublishData({
      title: appState.title,
      content: appState.content,
      selectedPlatforms: appState.selectedPlatforms,
      imagePreviews: appState.imagePreviews // 支持多图片数据
    });
  } catch (error) {
    console.error('Failed to save to storage:', error);
  }
}

// 事件处理函数
function handleTitleChange(event) {
  appState.title = event.target.value;
  saveToStorageData();
}

function handleContentChange(event) {
  appState.content = event.target.value;
  saveToStorageData();
}

// 文件上传处理器类 - 优化后的实现
class ImageUploadHandler {
  constructor() {
    this.processedCount = 0;
    this.totalFiles = 0;
    this.inputElement = null;
  }

  // 验证单个文件
  validateFile(file) {
    if (!IMAGE_CONFIG.allowedTypes.includes(file.type)) {
      return { valid: false, error: `不支持的文件格式: ${file.name}` };
    }

    if (file.size > IMAGE_CONFIG.maxFileSize) {
      return { valid: false, error: `文件过大: ${file.name} (最大5MB)` };
    }

    return { valid: true };
  }

  // 生成唯一ID
  generateUniqueId() {
    return `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // 处理单个文件完成
  handleFileComplete(success = true) {
    this.processedCount++;

    if (this.processedCount === this.totalFiles) {
      this.finishUpload();
    }
  }

  // 完成上传处理
  finishUpload() {
    updateImagePreview();
    saveToStorageData();

    if (this.inputElement) {
      this.inputElement.value = ''; // 清空文件输入
    }

    // 重置计数器
    this.processedCount = 0;
    this.totalFiles = 0;
  }

  // 处理文件上传
  handleUpload(event) {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    this.inputElement = event.target;

    // 检查是否超过最大图片数量
    const remainingSlots = IMAGE_CONFIG.maxImages - appState.imagePreviews.length;
    if (remainingSlots <= 0) {
      showNotification(`最多只能上传 ${IMAGE_CONFIG.maxImages} 张图片`, 'error');
      return;
    }

    // 处理选中的文件
    const filesToProcess = Array.from(files).slice(0, remainingSlots);
    this.totalFiles = filesToProcess.length;
    this.processedCount = 0;

    filesToProcess.forEach((file) => {
      // 验证文件
      const validation = this.validateFile(file);
      if (!validation.valid) {
        showNotification(validation.error, 'error');
        this.handleFileComplete(false);
        return;
      }

      // 读取文件
      const reader = new FileReader();

      reader.onload = (e) => {
        const imageData = {
          id: this.generateUniqueId(),
          name: file.name,
          size: file.size,
          dataUrl: e.target.result
        };

        appState.imagePreviews.push(imageData);
        this.handleFileComplete(true);
      };

      reader.onerror = () => {
        showNotification(`读取文件失败: ${file.name}`, 'error');
        this.handleFileComplete(false);
      };

      reader.readAsDataURL(file);
    });
  }
}

// 创建全局上传处理器实例
const imageUploadHandler = new ImageUploadHandler();

// 简化的上传函数
function handleImageUpload(event) {
  imageUploadHandler.handleUpload(event);
}

function togglePlatform(platform) {
  const isSelected = appState.selectedPlatforms.find(p => p.id === platform.id);
  if (isSelected) {
    appState.selectedPlatforms = appState.selectedPlatforms.filter(p => p.id !== platform.id);
  } else {
    appState.selectedPlatforms.push(platform);
  }
  updatePlatformSelection();
  saveToStorageData();
}

async function handleStartPublish() {
  if (!appState.content.trim()) {
    alert('请输入内容');
    return;
  }
  
  if (appState.selectedPlatforms.length === 0) {
    alert('请选择至少一个平台');
    return;
  }

  appState.isPublishing = true;
  updateSyncButton();

  try {
    // 发送消息到后台脚本
    await chrome.runtime.sendMessage({
      action: 'startPublish',
      data: { 
        title: appState.title, 
        content: appState.content, 
        platforms: appState.selectedPlatforms 
      }
    });
    
    // 打开侧边栏
    await chrome.sidePanel.open({ windowId: chrome.windows.WINDOW_ID_CURRENT });
    
    // 显示成功提示
    showNotification('发布任务已启动，请查看侧边栏监控进度', 'success');
    
  } catch (error) {
    console.error('Failed to start publish:', error);
    appState.isPublishing = false;
    updateSyncButton();
    showNotification('启动发布失败，请重试', 'error');
  }
}

// UI更新函数
function updateUI() {
  const titleInput = document.getElementById('title-input');
  const contentTextarea = document.getElementById('content-textarea');

  if (titleInput) titleInput.value = appState.title;
  if (contentTextarea) contentTextarea.value = appState.content;

  updatePlatformSelection();
  updateImagePreview();
  updateSyncButton();
}

// DOM元素缓存 - 避免重复查询
const domCache = {
  previewContainer: null,
  imageCountDisplay: null,
  clearAllBtn: null,
  gridContainer: null,

  // 初始化缓存
  init() {
    this.previewContainer = document.getElementById('image-preview');
    this.imageCountDisplay = document.getElementById('image-count');
    this.clearAllBtn = document.getElementById('clear-all-images');
  },

  // 获取或创建网格容器
  getGridContainer() {
    if (!this.gridContainer || !this.gridContainer.parentNode) {
      this.gridContainer = document.createElement('div');
      this.gridContainer.className = 'image-grid';
    }
    return this.gridContainer;
  }
};

// 创建单个图片预览元素
function createImagePreviewElement(imageData, index) {
  const previewDiv = document.createElement('div');
  previewDiv.className = 'image-preview-container';
  previewDiv.dataset.imageId = imageData.id; // 添加数据属性便于查找

  // 创建图片元素
  const img = document.createElement('img');
  img.src = imageData.dataUrl;
  img.alt = `预览图片 ${index + 1}`;
  img.title = `${imageData.name} (${formatFileSize(imageData.size)})`;
  img.loading = 'lazy'; // 懒加载优化

  // 创建删除按钮
  const removeBtn = document.createElement('button');
  removeBtn.className = 'image-remove-btn';
  removeBtn.textContent = '×';
  removeBtn.title = `删除图片: ${imageData.name}`;
  removeBtn.setAttribute('aria-label', `删除图片: ${imageData.name}`);

  // 使用事件委托优化 - 在父容器上绑定事件
  removeBtn.dataset.imageId = imageData.id;

  // 组装预览元素
  previewDiv.appendChild(img);
  previewDiv.appendChild(removeBtn);

  return previewDiv;
}

// 优化后的图片预览更新函数
function updateImagePreview() {
  // 确保DOM缓存已初始化
  if (!domCache.previewContainer) {
    domCache.init();
  }

  const { previewContainer, imageCountDisplay, clearAllBtn } = domCache;

  if (!previewContainer) return;

  const imageCount = appState.imagePreviews.length;

  if (imageCount > 0) {
    // 获取或创建网格容器
    const gridContainer = domCache.getGridContainer();

    // 清空网格容器
    gridContainer.innerHTML = '';

    // 创建图片预览元素
    const fragment = document.createDocumentFragment(); // 使用文档片段优化性能

    appState.imagePreviews.forEach((imageData, index) => {
      const previewElement = createImagePreviewElement(imageData, index);
      fragment.appendChild(previewElement);
    });

    gridContainer.appendChild(fragment);

    // 如果网格容器不在预览容器中，则添加
    if (!gridContainer.parentNode) {
      previewContainer.appendChild(gridContainer);
    }

    previewContainer.style.display = 'block';
  } else {
    previewContainer.style.display = 'none';
  }

  // 更新图片计数显示
  updateImageCount(imageCount);

  // 更新清空按钮显示
  updateClearAllButton(imageCount);
}

// 分离的更新函数 - 提高可维护性
function updateImageCount(count) {
  if (domCache.imageCountDisplay) {
    const maxCount = IMAGE_CONFIG.maxImages;
    domCache.imageCountDisplay.textContent = `已上传 ${count}/${maxCount} 张图片`;
    domCache.imageCountDisplay.style.display = count > 0 ? 'block' : 'none';
  }
}

function updateClearAllButton(count) {
  if (domCache.clearAllBtn) {
    domCache.clearAllBtn.style.display = count > 0 ? 'inline-block' : 'none';
  }
}

function removeImage(imageId) {
  Utils.safeExecute(() => {
    if (imageId === undefined) {
      // 删除所有图片
      appState.imagePreviews = [];
    } else {
      // 删除指定ID的图片
      const initialLength = appState.imagePreviews.length;
      appState.imagePreviews = appState.imagePreviews.filter(img => img.id !== imageId);

      // 验证删除是否成功
      if (appState.imagePreviews.length === initialLength) {
        console.warn(`图片删除失败: 未找到ID为 ${imageId} 的图片`);
        return;
      }
    }

    updateImagePreview();
    saveToStorageData();
  }, '删除图片失败');
}

// 工具函数集合
const Utils = {
  // 格式化文件大小显示
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // 防抖函数 - 优化频繁操作
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  // 错误边界处理
  safeExecute(fn, errorMessage = '操作失败') {
    try {
      return fn();
    } catch (error) {
      console.error(`${errorMessage}:`, error);
      showNotification(errorMessage, 'error');
      return null;
    }
  }
};

// 向后兼容
function formatFileSize(bytes) {
  return Utils.formatFileSize(bytes);
}

// 清空所有图片
function clearAllImages() {
  appState.imagePreviews = [];
  updateImagePreview();
  saveToStorageData();

  // 清空文件输入
  const imageUpload = document.getElementById('image-upload');
  if (imageUpload) {
    imageUpload.value = '';
  }
}

function updatePlatformSelection() {
  SUPPORTED_PLATFORMS.forEach(platform => {
    const checkbox = document.getElementById(`platform-${platform.id}`);
    if (checkbox) {
      checkbox.checked = appState.selectedPlatforms.some(p => p.id === platform.id);
    }
  });
  
  // 更新选中计数
  const selectedCount = document.getElementById('selected-count');
  if (selectedCount) {
    const count = appState.selectedPlatforms.length;
    selectedCount.textContent = count > 0 ? `已选择 ${count} 个平台` : '请选择发布平台';
  }
}

function updateSyncButton() {
  const syncButton = document.getElementById('sync-button');
  if (syncButton) {
    syncButton.disabled = appState.isPublishing;
    const buttonText = syncButton.querySelector('.button-text');
    const buttonIcon = syncButton.querySelector('.button-icon');
    
    if (buttonText) {
      buttonText.textContent = appState.isPublishing ? '发布中...' : '开始同步';
    }
    
    if (buttonIcon && appState.isPublishing) {
      buttonIcon.classList.add('loading-spinner');
    } else if (buttonIcon) {
      buttonIcon.classList.remove('loading-spinner');
    }
  }
}

// 通知系统
function showNotification(message, type = 'info') {
  const notification = document.createElement('div');
  notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
    type === 'success' ? 'bg-green-500 text-white' :
    type === 'error' ? 'bg-red-500 text-white' :
    'bg-blue-500 text-white'
  }`;
  notification.textContent = message;
  
  document.body.appendChild(notification);
  
  // 3秒后自动移除
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

// 创建页面内容
function createPageContent() {
  const root = document.getElementById('main-root');
  
  root.innerHTML = `
    <!-- Header -->
    <header class="bg-white border-b border-gray-200 shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="text-xl font-semibold text-gray-900">动态发布助手</h1>
            </div>
          </div>
          <div class="flex items-center space-x-4">
            <span id="selected-count" class="text-sm text-gray-500">请选择发布平台</span>
            <button 
              id="sync-button"
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg class="button-icon w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
              </svg>
              <span class="button-text">开始同步</span>
            </button>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main class="content-area">
      <div class="form-grid">
        <!-- Content Form -->
        <div class="space-y-6">
          <!-- Content Card -->
          <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">发布内容</h2>
              <p class="mt-1 text-sm text-gray-500">填写要发布的动态内容</p>
            </div>
            <div class="p-6 space-y-6">
              <!-- Title Input -->
              <div>
                <label for="title-input" class="block text-sm font-medium text-gray-700 mb-2">
                  标题 <span class="text-gray-400">(可选)</span>
                </label>
                <input 
                  id="title-input"
                  type="text"
                  class="input-field"
                  placeholder="输入动态标题..."
                />
              </div>

              <!-- Content Textarea -->
              <div>
                <label for="content-textarea" class="block text-sm font-medium text-gray-700 mb-2">
                  内容 <span class="text-red-500">*</span>
                </label>
                <textarea
                  id="content-textarea"
                  class="textarea-field"
                  rows="8"
                  placeholder="分享你的想法..."
                ></textarea>
                <p class="mt-1 text-xs text-gray-500">支持文本内容，将自动适配各平台格式</p>
              </div>

              <!-- Image Upload -->
              <div>
                <div class="flex items-center justify-between mb-2">
                  <label class="block text-sm font-medium text-gray-700">图片</label>
                  <div class="flex items-center space-x-2">
                    <span id="image-count" class="text-xs text-gray-500" style="display: none;"></span>
                    <button
                      id="clear-all-images"
                      type="button"
                      class="text-xs text-red-600 hover:text-red-800 hidden"
                      title="清空所有图片"
                    >
                      清空全部
                    </button>
                  </div>
                </div>

                <div class="space-y-3">
                  <!-- 上传按钮区域 -->
                  <div class="flex items-start space-x-4">
                    <label class="cursor-pointer">
                      <div class="flex items-center justify-center w-20 h-20 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition-colors">
                        <div class="text-center">
                          <svg class="mx-auto h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                          </svg>
                          <span class="mt-1 block text-xs text-gray-500">上传</span>
                        </div>
                      </div>
                      <input
                        id="image-upload"
                        type="file"
                        accept="image/jpeg,image/png,image/gif,image/webp"
                        multiple
                        class="hidden"
                      />
                    </label>
                  </div>

                  <!-- 图片预览区域 -->
                  <div id="image-preview" style="display: none;"></div>
                </div>

                <p class="mt-2 text-xs text-gray-500">
                  支持 JPG、PNG、GIF、WebP 格式，最多9张，单张最大5MB
                </p>
              </div>
            </div>
          </div>


        </div>

        <!-- Platform Selection -->
        <div class="space-y-6">
          <div class="bg-white rounded-lg border border-gray-200 shadow-sm">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">选择平台</h2>
              <p class="mt-1 text-sm text-gray-500">选择要发布的社交媒体平台</p>
            </div>
            <div class="p-6">
              <div class="space-y-4" id="platform-list">
                ${SUPPORTED_PLATFORMS.map(platform => `
                  <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-blue-300 transition-colors cursor-pointer platform-item" data-platform-id="${platform.id}">
                    <input 
                      type="checkbox" 
                      id="platform-${platform.id}"
                      class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                    />
                    <div class="ml-4 flex-1">
                      <div class="flex items-center">
                        <div class="w-4 h-4 rounded-sm ${platform.color} mr-3"></div>
                        <span class="text-sm font-medium text-gray-900">${platform.name}</span>
                        ${platform.user ? `<span class="ml-2 text-xs text-gray-500">${platform.user}</span>` : ''}
                      </div>
                    </div>
                  </div>
                `).join('')}
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  `;
}

// 事件委托处理器
function setupEventDelegation() {
  // 为图片预览容器设置事件委托
  const previewContainer = document.getElementById('image-preview');
  if (previewContainer) {
    previewContainer.addEventListener('click', (event) => {
      // 处理删除按钮点击
      if (event.target.classList.contains('image-remove-btn')) {
        const imageId = event.target.dataset.imageId;
        if (imageId) {
          removeImage(imageId);
        }
      }
    });
  }
}

// 绑定事件监听器
function bindEventListeners() {
  // 标题输入
  const titleInput = document.getElementById('title-input');
  if (titleInput) {
    titleInput.addEventListener('input', handleTitleChange);
  }

  // 内容输入
  const contentTextarea = document.getElementById('content-textarea');
  if (contentTextarea) {
    contentTextarea.addEventListener('input', handleContentChange);
  }

  // 图片上传
  const imageUpload = document.getElementById('image-upload');
  if (imageUpload) {
    imageUpload.addEventListener('change', handleImageUpload);
  }

  // 清空所有图片按钮
  const clearAllBtn = document.getElementById('clear-all-images');
  if (clearAllBtn) {
    clearAllBtn.addEventListener('click', () => {
      if (confirm('确定要删除所有图片吗？')) {
        clearAllImages();
      }
    });
  }

  // 设置事件委托
  setupEventDelegation();

  // 开始同步按钮
  const syncButton = document.getElementById('sync-button');
  if (syncButton) {
    syncButton.addEventListener('click', handleStartPublish);
  }

  // 平台选择
  SUPPORTED_PLATFORMS.forEach(platform => {
    const checkbox = document.getElementById(`platform-${platform.id}`);
    const platformItem = document.querySelector(`[data-platform-id="${platform.id}"]`);
    
    if (checkbox) {
      checkbox.addEventListener('change', () => togglePlatform(platform));
    }
    
    // 点击整个平台项也能切换选择状态
    if (platformItem) {
      platformItem.addEventListener('click', (e) => {
        if (e.target !== checkbox) {
          checkbox.checked = !checkbox.checked;
          togglePlatform(platform);
        }
      });
    }
  });
}

// 页面初始化
function initializePage() {
  console.log('Initializing main page...');

  // 创建页面内容
  createPageContent();

  // 初始化DOM缓存
  domCache.init();

  // 绑定事件监听器
  bindEventListeners();

  // 加载保存的数据
  loadFromStorageData();

  console.log('Main page initialized successfully');
}

// 页面加载完成后初始化
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializePage);
} else {
  initializePage();
}
