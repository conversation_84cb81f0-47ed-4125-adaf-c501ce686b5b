// 微博平台适配器
console.log('微博内容脚本已加载');

class WeiboAdapter {
  constructor() {
    this.platform = 'weibo';
    this.selectors = {
      contentTextarea: 'textarea[placeholder*="有什么新鲜事想告诉大家"]',
      publishButton: 'button[action-type="submit"]',
      imageUpload: 'input[type="file"][accept*="image"]'
    };
  }

  async waitForElement(selector, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const startTime = Date.now();
      
      const check = () => {
        const element = document.querySelector(selector);
        if (element) {
          resolve(element);
          return;
        }
        
        if (Date.now() - startTime > timeout) {
          reject(new Error(`Element ${selector} not found within ${timeout}ms`));
          return;
        }
        
        setTimeout(check, 100);
      };
      
      check();
    });
  }

  async fillContent(selector, content) {
    try {
      const element = await this.waitForElement(selector);
      
      // 清空现有内容
      element.value = '';
      element.focus();
      
      // 模拟用户输入
      element.value = content;
      
      // 触发输入事件
      element.dispatchEvent(new Event('input', { bubbles: true }));
      element.dispatchEvent(new Event('change', { bubbles: true }));
      
      console.log('Content filled successfully');
      return true;
    } catch (error) {
      console.error('Failed to fill content:', error);
      throw error;
    }
  }

  async clickElement(selector) {
    try {
      const element = await this.waitForElement(selector);
      element.click();
      console.log('Element clicked successfully');
      return true;
    } catch (error) {
      console.error('Failed to click element:', error);
      throw error;
    }
  }

  async publish(data) {
    console.log('开始微博发布流程:', data);
    
    try {
      const { title, content } = data;
      const fullContent = title ? `${title}\n${content}` : content;
      
      // 1. 等待页面加载完成
      await this.waitForElement(this.selectors.contentTextarea, 15000);
      console.log('微博发布页面加载完成');
      
      // 2. 填充内容
      await this.fillContent(this.selectors.contentTextarea, fullContent);
      console.log('内容填充完成');
      
      // 3. 等待一下让页面处理
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 4. 点击发布按钮
      await this.clickElement(this.selectors.publishButton);
      console.log('发布按钮已点击');
      
      // 5. 等待发布完成
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 6. 检查是否发布成功（简化处理）
      console.log('微博发布完成');
      
      return {
        success: true,
        message: '发布成功',
        url: window.location.href
      };
      
    } catch (error) {
      console.error('微博发布失败:', error);
      return {
        success: false,
        message: error.message || '发布失败'
      };
    }
  }
}

// 创建适配器实例
const weiboAdapter = new WeiboAdapter();

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('微博内容脚本收到消息:', message);
  
  if (message.action === 'publish') {
    weiboAdapter.publish(message.data)
      .then(result => {
        console.log('微博发布结果:', result);
        sendResponse(result);
      })
      .catch(error => {
        console.error('微博发布错误:', error);
        sendResponse({
          success: false,
          message: error.message || '发布失败'
        });
      });
    
    return true; // 保持消息通道开放
  }
});

console.log('微博适配器初始化完成');
