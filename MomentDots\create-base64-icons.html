<!DOCTYPE html>
<html>
<head>
    <title>生成Base64图标</title>
</head>
<body>
    <h2>动态发布助手 - 图标生成器</h2>
    <canvas id="canvas" width="128" height="128" style="border: 1px solid #ccc;"></canvas>
    <br><br>
    <button onclick="generateIcons()">生成图标</button>
    <div id="output"></div>

    <script>
        function generateIcons() {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, 128, 128);
            
            // 绘制背景圆形
            ctx.fillStyle = '#3B82F6';
            ctx.beginPath();
            ctx.arc(64, 64, 60, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制发送图标
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.moveTo(32, 64);
            ctx.lineTo(96, 32);
            ctx.lineTo(80, 64);
            ctx.lineTo(96, 96);
            ctx.lineTo(32, 64);
            ctx.closePath();
            ctx.fill();
            
            // 绘制装饰点
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.beginPath();
            ctx.arc(48, 48, 3, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(80, 48, 3, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(48, 80, 3, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(80, 80, 3, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制边框
            ctx.strokeStyle = '#1E40AF';
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.arc(64, 64, 60, 0, 2 * Math.PI);
            ctx.stroke();
            
            // 生成不同尺寸的图标
            generateIcon(128, 'icon128.png');
            generateIcon(48, 'icon48.png');
            generateIcon(32, 'icon32.png');
            generateIcon(16, 'icon16.png');
        }
        
        function generateIcon(size, filename) {
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = size;
            tempCanvas.height = size;
            const tempCtx = tempCanvas.getContext('2d');
            
            // 缩放绘制原图标
            tempCtx.drawImage(document.getElementById('canvas'), 0, 0, size, size);
            
            // 转换为数据URL
            const dataURL = tempCanvas.toDataURL('image/png');
            
            // 显示下载链接
            const output = document.getElementById('output');
            const link = document.createElement('a');
            link.href = dataURL;
            link.download = filename;
            link.textContent = `下载 ${filename}`;
            link.style.display = 'block';
            link.style.margin = '5px 0';
            link.style.padding = '10px';
            link.style.backgroundColor = '#f0f0f0';
            link.style.textDecoration = 'none';
            link.style.color = '#333';
            link.style.border = '1px solid #ccc';
            output.appendChild(link);
            
            // 显示Base64数据
            const pre = document.createElement('pre');
            pre.style.fontSize = '10px';
            pre.style.backgroundColor = '#f8f8f8';
            pre.style.padding = '10px';
            pre.style.border = '1px solid #ddd';
            pre.style.marginTop = '5px';
            pre.style.wordBreak = 'break-all';
            pre.textContent = `${filename} Base64:\n${dataURL}`;
            output.appendChild(pre);
        }
        
        // 页面加载时自动生成图标
        window.onload = generateIcons;
    </script>
</body>
</html>
